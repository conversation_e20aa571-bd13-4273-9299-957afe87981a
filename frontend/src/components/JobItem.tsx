import React, { useState } from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  IconButton,
  Chip,
  Button,
  Collapse,
  Tooltip,
  Avatar,
  CircularProgress
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Download,
  CheckCircle,
  Error,
  Cancel,
  PlayArrow,
  Photo,
  VideoLibrary,
  Schedule,
  Compress as CompressIcon,
  CloudUpload,
  Info
} from '@mui/icons-material';
import { JobData } from './JobsPanel';

interface JobItemProps {
  job: JobData;
  mediaItem?: {
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  };
  token: string | null;
  compact?: boolean; // New prop for compact display in grouped view
}

const JobItem: React.FC<JobItemProps> = ({ job, mediaItem, token, compact = false }) => {
  const [expanded, setExpanded] = useState(false);
  const [downloading, setDownloading] = useState(false);

  const isCompleted = job.status.toLowerCase() === 'completed';
  const isFailed = job.status.toLowerCase() === 'failed';
  const isCancelled = job.status.toLowerCase() === 'cancelled';
  const isProcessing = !isCompleted && !isFailed && !isCancelled;

  const getStatusIcon = () => {
    if (isCompleted) return <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />;
    if (isFailed) return <Error sx={{ fontSize: 16, color: 'error.main' }} />;
    if (isCancelled) return <Cancel sx={{ fontSize: 16, color: 'text.disabled' }} />;
    return <CircularProgress size={16} />;
  };

  const getStatusColor = () => {
    if (isCompleted) return 'success';
    if (isFailed) return 'error';
    if (isCancelled) return 'default';
    return 'primary';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const handleDownload = async () => {
    if (!token || !isCompleted) return;
    
    setDownloading(true);
    try {
      // TODO: Implement download endpoint call
      const response = await fetch(`/api/media/jobs/${job.jobId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `compressed_${job.filename || 'media'}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Download failed:', response.statusText);
      }
    } catch (error) {
      console.error('Download error:', error);
    } finally {
      setDownloading(false);
    }
  };

  const getMediaPreview = () => {
    if (mediaItem?.baseUrl) {
      return (
        <Avatar
          src={`${mediaItem.baseUrl}=w80-h80-c`}
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            bgcolor: 'grey.200'
          }}
        >
          {job.mediaType === 'Video' ? (
            <VideoLibrary sx={{ fontSize: 20, color: 'text.secondary' }} />
          ) : (
            <Photo sx={{ fontSize: 20, color: 'text.secondary' }} />
          )}
        </Avatar>
      );
    }
    
    return (
      <Avatar
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: 'grey.200'
        }}
      >
        {job.mediaType === 'Video' ? (
          <VideoLibrary sx={{ fontSize: 20, color: 'text.secondary' }} />
        ) : (
          <Photo sx={{ fontSize: 20, color: 'text.secondary' }} />
        )}
      </Avatar>
    );
  };

  return (
    <Box sx={{ p: compact ? 1 : 2 }}>
      {/* Main Job Info */}
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: compact ? 1 : 1.5 }}>
        {/* Media Preview - only show in non-compact mode */}
        {!compact && getMediaPreview()}
        
        {/* Job Details */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          {/* Filename and Status */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                color: 'text.primary',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flex: 1
              }}
            >
              {job.filename || mediaItem?.filename || 'Unknown file'}
            </Typography>
            {getStatusIcon()}
          </Box>
          
          {/* Status Message */}
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              display: 'block',
              mb: 1
            }}
          >
            {job.message}
          </Typography>
          
          {/* Progress Bar (for active jobs) */}
          {isProcessing && (
            <LinearProgress
              variant="determinate"
              value={job.progress}
              sx={{
                height: 4,
                borderRadius: 2,
                mb: 1,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 2
                }
              }}
            />
          )}
          
          {/* Job Info Chips */}
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 1 }}>
            <Chip
              label={job.quality}
              size="small"
              variant="outlined"
              sx={{ height: 20, fontSize: '0.65rem' }}
            />
            <Chip
              label={job.mediaType}
              size="small"
              variant="outlined"
              sx={{ height: 20, fontSize: '0.65rem' }}
            />
            {job.uploadToGooglePhotos && (
              <Chip
                icon={<CloudUpload sx={{ fontSize: 12 }} />}
                label="Upload"
                size="small"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.65rem' }}
              />
            )}
          </Box>
        </Box>
        
        {/* Actions */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, alignItems: 'flex-end' }}>
          {isCompleted && (
            <Button
              variant="outlined"
              size="small"
              startIcon={downloading ? <CircularProgress size={12} /> : <Download />}
              onClick={handleDownload}
              disabled={downloading || !token}
              sx={{
                minWidth: 'auto',
                px: 1,
                py: 0.5,
                fontSize: '0.7rem',
                textTransform: 'none'
              }}
            >
              {downloading ? 'Downloading...' : 'Download'}
            </Button>
          )}
          
          <IconButton
            size="small"
            onClick={() => setExpanded(!expanded)}
            sx={{ color: 'text.secondary' }}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>
      </Box>
      
      {/* Expanded Details */}
      <Collapse in={expanded} timeout={200}>
        <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, fontSize: '0.75rem' }}>
            {/* Left Column */}
            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
                <Schedule sx={{ fontSize: 14, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  Started: {formatDate(job.createdAt)}
                </Typography>
              </Box>
              
              {job.completedAt && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
                  <CheckCircle sx={{ fontSize: 14, color: 'success.main' }} />
                  <Typography variant="caption" color="text.secondary">
                    Completed: {formatDate(job.completedAt)}
                  </Typography>
                </Box>
              )}
              
              {job.error && (
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 0.5, mb: 1 }}>
                  <Error sx={{ fontSize: 14, color: 'error.main', mt: 0.1 }} />
                  <Typography variant="caption" color="error.main" sx={{ wordBreak: 'break-word' }}>
                    {job.error}
                  </Typography>
                </Box>
              )}
            </Box>
            
            {/* Right Column */}
            <Box>
              {job.compressionRatio && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
                  <CompressIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    Ratio: {(job.compressionRatio * 100).toFixed(1)}%
                  </Typography>
                </Box>
              )}
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
                <Info sx={{ fontSize: 14, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  Job ID: {job.jobId.substring(0, 8)}...
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Collapse>
    </Box>
  );
};

export default JobItem;
