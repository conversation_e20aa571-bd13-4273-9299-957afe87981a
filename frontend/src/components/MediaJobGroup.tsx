import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  IconButton,
  Collapse,
  Avatar,
  Chip,
  Divider,
  List,
  ListItem
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Photo,
  VideoLibrary
} from '@mui/icons-material';
import JobItem from './JobItem';
import { JobData } from './JobsPanel';

interface MediaJobGroupProps {
  mediaItemId: string;
  jobs: JobData[];
  mediaItem?: {
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  };
  token: string | null;
}

const MediaJobGroup: React.FC<MediaJobGroupProps> = ({
  mediaItemId,
  jobs,
  mediaItem,
  token
}) => {
  const [expanded, setExpanded] = useState(false);

  // Get the most recent job for the main display
  const latestJob = jobs[0];
  const completedJobs = jobs.filter(job => job.status.toLowerCase() === 'completed');
  const activeJobs = jobs.filter(job => !['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase()));
  const hasActiveJobs = activeJobs.length > 0;

  const getMediaPreview = () => {
    if (mediaItem?.baseUrl) {
      return (
        <Avatar
          src={`${mediaItem.baseUrl}=w60-h60-c`}
          sx={{
            width: 50,
            height: 50,
            borderRadius: 1,
            bgcolor: 'grey.200'
          }}
        >
          {latestJob.mediaType === 'Video' ? (
            <VideoLibrary sx={{ fontSize: 24, color: 'text.secondary' }} />
          ) : (
            <Photo sx={{ fontSize: 24, color: 'text.secondary' }} />
          )}
        </Avatar>
      );
    }
    
    return (
      <Avatar
        sx={{
          width: 50,
          height: 50,
          borderRadius: 1,
          bgcolor: 'grey.200'
        }}
      >
        {latestJob.mediaType === 'Video' ? (
          <VideoLibrary sx={{ fontSize: 24, color: 'text.secondary' }} />
        ) : (
          <Photo sx={{ fontSize: 24, color: 'text.secondary' }} />
        )}
      </Avatar>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Main Media Item Info */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
        {/* Media Preview */}
        {getMediaPreview()}
        
        {/* Media Details */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          {/* Filename */}
          <Typography
            variant="body1"
            sx={{
              fontWeight: 500,
              color: 'text.primary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              mb: 0.5
            }}
          >
            {latestJob.filename || mediaItem?.filename || 'Unknown file'}
          </Typography>
          
          {/* Job Summary */}
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 0.5 }}>
            <Chip
              label={`${jobs.length} job${jobs.length > 1 ? 's' : ''}`}
              size="small"
              variant="outlined"
              sx={{ height: 20, fontSize: '0.65rem' }}
            />
            {completedJobs.length > 0 && (
              <Chip
                label={`${completedJobs.length} completed`}
                size="small"
                color="success"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.65rem' }}
              />
            )}
            {hasActiveJobs && (
              <Chip
                label={`${activeJobs.length} active`}
                size="small"
                color="primary"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.65rem' }}
              />
            )}
            <Chip
              label={latestJob.mediaType}
              size="small"
              variant="outlined"
              sx={{ height: 20, fontSize: '0.65rem' }}
            />
          </Box>
          
          {/* Latest Job Status */}
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              display: 'block'
            }}
          >
            Latest: {latestJob.message}
          </Typography>
        </Box>
        
        {/* Expand/Collapse Button */}
        <IconButton
          size="small"
          onClick={() => setExpanded(!expanded)}
          sx={{ color: 'text.secondary' }}
        >
          {expanded ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </Box>
      
      {/* Expanded Job List */}
      <Collapse in={expanded} timeout={200}>
        <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider' }}>
          <Typography
            variant="subtitle2"
            sx={{
              color: 'text.secondary',
              mb: 1,
              fontSize: '0.75rem',
              fontWeight: 500
            }}
          >
            Compression Jobs ({jobs.length})
          </Typography>
          
          <List sx={{ p: 0 }}>
            {jobs.map((job, index) => (
              <React.Fragment key={job.jobId}>
                <ListItem sx={{ p: 0 }}>
                  <JobItem
                    job={job}
                    mediaItem={mediaItem}
                    token={token}
                    compact={true}
                  />
                </ListItem>
                {index < jobs.length - 1 && (
                  <Divider sx={{ my: 1, ml: 6 }} />
                )}
              </React.Fragment>
            ))}
          </List>
        </Box>
      </Collapse>
    </Box>
  );
};

export default MediaJobGroup;
